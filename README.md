# AI Content Aggregator

## Project Overview
A web application that aggregates and displays curated AI-related content from Twitter, LinkedIn, and reputable news sources, providing users with concise, categorized updates for a 10-minute daily review.

## Architecture

### Data Sources
- **Twitter API v2**: Fetch tweets with AI hashtags and from key accounts
- **LinkedIn**: Access public posts via third-party services (Lix API)
- **News Sources**: Aggregate via RSS feeds from WIRED, MIT AI News, Google AI Blog, OpenAI Blog

### Data Processing
- **Summarization**: Automated AI-powered summarization for RSS articles using OpenAI GPT or compatible APIs
- **Categorization**: Classify into Research, Product Releases, Opinion Pieces, Newsletters, Social Media

### Backend
- **Database**: PostgreSQL for content storage
- **API**: RESTful endpoints for frontend data access

### Frontend
- **Framework**: React for responsive UI
- **Features**: Search, filters, category tabs
- **Deployment**: Vercel

## Setup Instructions
1. Clone repository
2. Install dependencies
3. Configure API keys for data sources and summarization service
4. Set up SQL Server database
5. Run development server

### Environment Variables
Copy `.env.example` to `.env` and configure:
- **Summarization API**: `SUMMARIZATION_API_KEY` (OpenAI API key or compatible service)
- **Database**: SQL Server connection parameters
- **Social APIs**: Twitter and LinkedIn credentials (optional)

## Development Tasks

### Phase 1: Data Collection
- [ ] Set up Twitter API integration
- [x] Implement LinkedIn data collection
- [ ] Configure RSS feed aggregation
- [ ] Create data models for unified storage

### Phase 2: Processing Pipeline
- [x] Develop AI summarization service
- [ ] Build categorization system
- [ ] Implement content filtering

### Phase 3: Backend Development
- [x] Create database schema
- [x] Develop RESTful API endpoints
- [ ] Set up authentication system

### Phase 4: Frontend Implementation
- [x] Design responsive UI components
- [x] Implement content display views
- [x] Add search and filtering functionality

### Phase 5: Deployment
- [x] Configure production environment
- [x] Set up CI/CD pipeline
- [x] Deploy to Vercel

## Status Updates
*This section will be automatically updated as tasks progress*