name: Daily Data Collection

on:
  schedule:
    # Run at 1:00 AM UTC every day
    - cron: '0 1 * * *'
  workflow_dispatch:  # Allow manual triggering

jobs:
  collect-data:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install schedule
        
    - name: Run data collection
      env:
        TWITTER_API_KEY: ${{ secrets.TWITTER_API_KEY }}
        TWITTER_API_SECRET: ${{ secrets.TWITTER_API_SECRET }}
        TWITTER_ACCESS_TOKEN: ${{ secrets.TWITTER_ACCESS_TOKEN }}
        TWITTER_ACCESS_SECRET: ${{ secrets.TWITTER_ACCESS_SECRET }}
        LINKEDIN_API_KEY: ${{ secrets.LINKEDIN_API_KEY }}
      run: |
        python collect_and_save_data.py --days-ago 1 --max-results 50
        
    - name: Commit and push if there are changes
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add data/*.json
        git diff --quiet && git diff --staged --quiet || git commit -m "Auto data collection $(date +'%Y-%m-%d')"
        git push
